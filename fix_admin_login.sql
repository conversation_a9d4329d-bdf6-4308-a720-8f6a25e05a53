-- 修复管理员登录问题的SQL脚本
-- 请在MySQL中执行此脚本

USE reading_share;

-- 1. 检查管理员用户是否存在
SELECT 'Checking admin user...' as step;
SELECT u.id, u.username, u.enabled, u.password 
FROM users u 
WHERE u.username = 'admin';

-- 2. 检查角色是否存在
SELECT 'Checking roles...' as step;
SELECT * FROM roles;

-- 3. 检查用户角色关联
SELECT 'Checking user roles...' as step;
SELECT u.username, r.name as role_name 
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

-- 4. 如果管理员用户不存在，创建它
INSERT IGNORE INTO users (username, password, nickname, enabled, create_time, update_time) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iIdp.F9.7M4j0CgkVxTfkSWJNWm6', '系统管理员', 1, NOW(), NOW());

-- 5. 确保角色存在
INSERT IGNORE INTO roles (name, description) VALUES 
('ROLE_ADMIN', '管理员'),
('ROLE_EDITOR', '编辑'),
('ROLE_USER', '普通用户');

-- 6. 获取管理员用户ID和管理员角色ID
SET @admin_user_id = (SELECT id FROM users WHERE username = 'admin');
SET @admin_role_id = (SELECT id FROM roles WHERE name = 'ROLE_ADMIN');

-- 7. 为管理员分配角色（如果还没有分配）
INSERT IGNORE INTO user_roles (user_id, role_id) 
VALUES (@admin_user_id, @admin_role_id);

-- 8. 确保管理员用户是启用状态
UPDATE users SET enabled = 1 WHERE username = 'admin';

-- 9. 验证修复结果
SELECT 'Final verification...' as step;
SELECT u.id, u.username, u.nickname, u.enabled, r.name as role_name 
FROM users u 
LEFT JOIN user_roles ur ON u.id = ur.user_id 
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.username = 'admin';

-- 10. 显示管理员密码信息（用于调试）
SELECT 'Admin password hash:' as info, password 
FROM users 
WHERE username = 'admin';

SELECT 'Admin login should work with username: admin, password: admin123' as instruction;
