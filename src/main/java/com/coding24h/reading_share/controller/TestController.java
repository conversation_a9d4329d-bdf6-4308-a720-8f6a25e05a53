package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/test")
public class TestController {

    @Autowired
    private UserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("/auth")
    @ResponseBody
    public String testAuth() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return "当前认证信息: " + auth.toString() +
               "<br>用户名: " + auth.getName() +
               "<br>权限: " + auth.getAuthorities();
    }

    @GetMapping("/login-test")
    public String loginTest(Model model) {
        model.addAttribute("message", "这是一个测试登录页面");
        return "test/login-test";
    }

    @GetMapping("/admin-check")
    @ResponseBody
    public String checkAdmin() {
        try {
            User admin = userService.findByUsername("admin");
            if (admin == null) {
                return "❌ 管理员用户不存在";
            }

            StringBuilder result = new StringBuilder();
            result.append("✅ 管理员用户存在<br>");
            result.append("用户名: ").append(admin.getUsername()).append("<br>");
            result.append("昵称: ").append(admin.getNickname()).append("<br>");
            result.append("启用状态: ").append(admin.getEnabled() ? "启用" : "禁用").append("<br>");
            result.append("角色数量: ").append(admin.getRoles() != null ? admin.getRoles().size() : 0).append("<br>");

            if (admin.getRoles() != null && !admin.getRoles().isEmpty()) {
                result.append("角色列表: ");
                admin.getRoles().forEach(role ->
                    result.append(role.getName()).append(" (").append(role.getDescription()).append(") ")
                );
                result.append("<br>");
            }

            // 测试密码验证
            boolean passwordMatch = passwordEncoder.matches("admin123", admin.getPassword());
            result.append("密码验证 (admin123): ").append(passwordMatch ? "✅ 正确" : "❌ 错误").append("<br>");

            return result.toString();
        } catch (Exception e) {
            return "❌ 检查管理员用户时出错: " + e.getMessage();
        }
    }

    @GetMapping("/password-test")
    @ResponseBody
    public String testPassword(@RequestParam String password) {
        try {
            User admin = userService.findByUsername("admin");
            if (admin == null) {
                return "❌ 管理员用户不存在";
            }

            boolean passwordMatch = passwordEncoder.matches(password, admin.getPassword());
            return "密码 '" + password + "' 验证结果: " + (passwordMatch ? "✅ 正确" : "❌ 错误");
        } catch (Exception e) {
            return "❌ 密码测试出错: " + e.getMessage();
        }
    }

    @GetMapping("/admin-debug")
    public String adminDebug() {
        return "test/admin-debug";
    }
}
