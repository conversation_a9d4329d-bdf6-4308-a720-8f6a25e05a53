package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Role;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.mapper.RoleMapper;
import com.coding24h.reading_share.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/admin-fix")
public class AdminFixController {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("/check")
    @ResponseBody
    public String checkAdminUser() {
        StringBuilder result = new StringBuilder();
        result.append("<h3>管理员用户检查报告</h3>");
        
        try {
            // 1. 检查管理员用户是否存在
            User admin = userMapper.findByUsername("admin");
            if (admin == null) {
                result.append("❌ 管理员用户不存在<br>");
                return result.toString();
            }
            
            result.append("✅ 管理员用户存在<br>");
            result.append("用户ID: ").append(admin.getId()).append("<br>");
            result.append("用户名: ").append(admin.getUsername()).append("<br>");
            result.append("昵称: ").append(admin.getNickname()).append("<br>");
            result.append("启用状态: ").append(admin.getEnabled() ? "✅ 启用" : "❌ 禁用").append("<br>");
            
            // 2. 检查密码
            boolean passwordMatch = passwordEncoder.matches("admin123", admin.getPassword());
            result.append("密码验证 (admin123): ").append(passwordMatch ? "✅ 正确" : "❌ 错误").append("<br>");
            result.append("密码哈希: ").append(admin.getPassword()).append("<br>");
            
            // 3. 检查角色
            if (admin.getRoles() == null || admin.getRoles().isEmpty()) {
                result.append("❌ 用户没有分配角色<br>");
            } else {
                result.append("✅ 用户角色: ");
                admin.getRoles().forEach(role -> 
                    result.append(role.getName()).append(" (").append(role.getDescription()).append(") ")
                );
                result.append("<br>");
                
                // 检查是否有管理员角色
                boolean hasAdminRole = admin.getRoles().stream()
                    .anyMatch(role -> "ROLE_ADMIN".equals(role.getName()));
                result.append("管理员角色: ").append(hasAdminRole ? "✅ 有" : "❌ 无").append("<br>");
            }
            
            // 4. 检查所有角色
            result.append("<br><strong>系统角色列表:</strong><br>");
            roleMapper.findAll().forEach(role -> 
                result.append("- ").append(role.getName()).append(" (").append(role.getDescription()).append(")<br>")
            );
            
        } catch (Exception e) {
            result.append("❌ 检查过程中出错: ").append(e.getMessage()).append("<br>");
            e.printStackTrace();
        }
        
        return result.toString();
    }
    
    @GetMapping("/page")
    public String adminFixPage() {
        return "admin-fix";
    }

    @GetMapping("/fix")
    @ResponseBody
    public String fixAdminUser() {
        StringBuilder result = new StringBuilder();
        result.append("<h3>管理员用户修复报告</h3>");
        
        try {
            // 1. 检查或创建管理员用户
            User admin = userMapper.findByUsername("admin");
            if (admin == null) {
                result.append("创建管理员用户...<br>");
                admin = new User();
                admin.setUsername("admin");
                admin.setPassword(passwordEncoder.encode("admin123"));
                admin.setNickname("系统管理员");
                admin.setEnabled(true);
                userMapper.insert(admin);
                result.append("✅ 管理员用户创建成功<br>");
            } else {
                result.append("✅ 管理员用户已存在<br>");
                
                // 确保用户是启用状态
                if (!admin.getEnabled()) {
                    admin.setEnabled(true);
                    userMapper.update(admin);
                    result.append("✅ 已启用管理员用户<br>");
                }
                
                // 检查密码是否正确
                if (!passwordEncoder.matches("admin123", admin.getPassword())) {
                    admin.setPassword(passwordEncoder.encode("admin123"));
                    userMapper.update(admin);
                    result.append("✅ 已重置管理员密码为 admin123<br>");
                }
            }
            
            // 2. 确保管理员角色存在
            Role adminRole = roleMapper.findByName("ROLE_ADMIN");
            if (adminRole == null) {
                result.append("创建管理员角色...<br>");
                adminRole = new Role();
                adminRole.setName("ROLE_ADMIN");
                adminRole.setDescription("管理员");
                roleMapper.insert(adminRole);
                result.append("✅ 管理员角色创建成功<br>");
            }
            
            // 3. 为管理员分配角色
            admin = userMapper.findByUsername("admin"); // 重新获取用户信息
            boolean hasAdminRole = admin.getRoles() != null && 
                admin.getRoles().stream().anyMatch(role -> "ROLE_ADMIN".equals(role.getName()));
            
            if (!hasAdminRole) {
                roleMapper.assignRoleToUser(admin.getId(), adminRole.getId());
                result.append("✅ 已为管理员分配管理员角色<br>");
            } else {
                result.append("✅ 管理员已有管理员角色<br>");
            }
            
            result.append("<br><strong>修复完成！</strong><br>");
            result.append("管理员登录信息:<br>");
            result.append("用户名: admin<br>");
            result.append("密码: admin123<br>");
            
        } catch (Exception e) {
            result.append("❌ 修复过程中出错: ").append(e.getMessage()).append("<br>");
            e.printStackTrace();
        }
        
        return result.toString();
    }
}
