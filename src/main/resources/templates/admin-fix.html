<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录问题修复工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <h1 class="text-center mb-4">管理员登录问题修复工具</h1>
                
                <div class="alert alert-info">
                    <h5>问题描述:</h5>
                    <p>管理员用户 (admin) 无法登录，但普通用户可以正常登录。</p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>诊断工具</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="checkAdmin()">
                                🔍 检查管理员用户状态
                            </button>
                            <button class="btn btn-success" onclick="fixAdmin()">
                                🔧 修复管理员用户
                            </button>
                            <button class="btn btn-info" onclick="testLogin()">
                                🧪 测试登录
                            </button>
                        </div>
                        
                        <div id="result" class="result-box" style="display: none;">
                            <div id="result-content"></div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>手动测试登录</h5>
                    </div>
                    <div class="card-body">
                        <form action="/login" method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" value="admin">
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="password" name="password" value="admin123">
                            </div>
                            <button type="submit" class="btn btn-primary">登录测试</button>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <a href="/" class="btn btn-secondary">返回首页</a>
                    <a href="/login" class="btn btn-outline-primary">登录页面</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult(content) {
            document.getElementById('result-content').innerHTML = content;
            document.getElementById('result').style.display = 'block';
        }
        
        function checkAdmin() {
            showResult('<div class="text-center"><div class="spinner-border" role="status"></div><br>正在检查...</div>');
            
            fetch('/admin-fix/check')
                .then(response => response.text())
                .then(data => {
                    showResult(data);
                })
                .catch(error => {
                    showResult('<div class="alert alert-danger">❌ 检查失败: ' + error + '</div>');
                });
        }
        
        function fixAdmin() {
            showResult('<div class="text-center"><div class="spinner-border" role="status"></div><br>正在修复...</div>');
            
            fetch('/admin-fix/fix')
                .then(response => response.text())
                .then(data => {
                    showResult(data);
                })
                .catch(error => {
                    showResult('<div class="alert alert-danger">❌ 修复失败: ' + error + '</div>');
                });
        }
        
        function testLogin() {
            showResult('<div class="text-center"><div class="spinner-border" role="status"></div><br>正在测试登录...</div>');
            
            // 创建一个隐藏的表单来测试登录
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/login';
            form.style.display = 'none';
            
            const usernameInput = document.createElement('input');
            usernameInput.name = 'username';
            usernameInput.value = 'admin';
            form.appendChild(usernameInput);
            
            const passwordInput = document.createElement('input');
            passwordInput.name = 'password';
            passwordInput.value = 'admin123';
            form.appendChild(passwordInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    </script>
</body>
</html>
