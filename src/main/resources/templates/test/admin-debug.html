<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员调试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>管理员用户调试信息</h1>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>管理员用户检查</h5>
            </div>
            <div class="card-body">
                <div id="admin-info">
                    <p>正在检查管理员用户信息...</p>
                </div>
                <button class="btn btn-primary" onclick="checkAdmin()">重新检查</button>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>密码测试</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="testPassword" class="form-label">测试密码:</label>
                    <input type="password" class="form-control" id="testPassword" value="admin123">
                </div>
                <button class="btn btn-secondary" onclick="testPassword()">测试密码</button>
                <div id="password-result" class="mt-2"></div>
            </div>
        </div>
    </div>

    <script>
        function checkAdmin() {
            fetch('/test/admin-check')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('admin-info').innerHTML = data;
                })
                .catch(error => {
                    document.getElementById('admin-info').innerHTML = '❌ 检查失败: ' + error;
                });
        }
        
        function testPassword() {
            const password = document.getElementById('testPassword').value;
            fetch('/test/password-test?password=' + encodeURIComponent(password))
                .then(response => response.text())
                .then(data => {
                    document.getElementById('password-result').innerHTML = data;
                })
                .catch(error => {
                    document.getElementById('password-result').innerHTML = '❌ 测试失败: ' + error;
                });
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkAdmin();
        };
    </script>
</body>
</html>
